import { Gem } from '../objects/Gem';
import { ScoreManager } from './ScoreManager';

/**
 * Class quản lý game board và logic chính
 */
export class GameBoard {
    private scene: Phaser.Scene;
    private grid: (Gem | null)[][];
    private gridSize: number = 8;
    private cellSize: number = 80;
    private startX: number;
    private startY: number;
    private selectedGem: Gem | null = null;
    private isProcessing: boolean = false;
    private scoreManager: ScoreManager;
    
    constructor(scene: Phaser.Scene, scoreManager: ScoreManager) {
        this.scene = scene;
        this.scoreManager = scoreManager;
        this.grid = [];

        // Tính toán vị trí trung tâm
        this.startX = (1024 - (this.gridSize * this.cellSize)) / 2;
        this.startY = (768 - (this.gridSize * this.cellSize)) / 2;

        this.initializeGrid();
        this.setupEventListeners();
    }
    
    /**
     * Khởi tạo grid với gems ngẫu nhiên
     */
    private initializeGrid(): void {
        // Tạo background cells
        this.createGridBackground();
        
        // Khởi tạo array 2D
        for (let row = 0; row < this.gridSize; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridSize; col++) {
                this.grid[row][col] = null;
            }
        }
        
        // Fill với gems ngẫu nhiên (tránh matches ban đầu)
        this.fillGridWithoutMatches();
    }
    
    /**
     * Tạo background cho grid
     */
    private createGridBackground(): void {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const x = this.startX + col * this.cellSize + this.cellSize / 2;
                const y = this.startY + row * this.cellSize + this.cellSize / 2;
                
                const cell = this.scene.add.sprite(x, y, 'grid_cell');
                cell.setDepth(-1);
            }
        }
    }
    
    /**
     * Fill grid với gems mà không tạo matches ban đầu
     */
    private fillGridWithoutMatches(): void {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                let gemType: number;
                let attempts = 0;
                
                do {
                    gemType = Phaser.Math.Between(0, 6); // 7 loại gem
                    attempts++;
                } while (this.wouldCreateMatch(row, col, gemType) && attempts < 10);
                
                this.createGem(row, col, gemType);
            }
        }
    }
    
    /**
     * Kiểm tra xem việc đặt gem type tại vị trí có tạo match không
     */
    private wouldCreateMatch(row: number, col: number, gemType: number): boolean {
        // Kiểm tra horizontal
        let horizontalCount = 1;
        
        // Check left
        for (let c = col - 1; c >= 0; c--) {
            if (this.grid[row][c] && this.grid[row][c]!.gemType === gemType) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        // Check right
        for (let c = col + 1; c < this.gridSize; c++) {
            if (this.grid[row][c] && this.grid[row][c]!.gemType === gemType) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        if (horizontalCount >= 3) return true;
        
        // Kiểm tra vertical
        let verticalCount = 1;
        
        // Check up
        for (let r = row - 1; r >= 0; r--) {
            if (this.grid[r][col] && this.grid[r][col]!.gemType === gemType) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        // Check down
        for (let r = row + 1; r < this.gridSize; r++) {
            if (this.grid[r][col] && this.grid[r][col]!.gemType === gemType) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        return verticalCount >= 3;
    }
    
    /**
     * Tạo gem tại vị trí grid
     */
    private createGem(row: number, col: number, gemType: number): void {
        const x = this.startX + col * this.cellSize + this.cellSize / 2;
        const y = this.startY + row * this.cellSize + this.cellSize / 2;
        
        const gem = new Gem(this.scene, x, y, gemType, col, row);
        this.grid[row][col] = gem;
    }
    
    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('gem-clicked', this.onGemClicked, this);

        // Show hint after inactivity
        this.scene.time.addEvent({
            delay: 5000,
            callback: this.showHint,
            callbackScope: this,
            loop: true
        });
    }
    
    /**
     * Xử lý khi gem được click
     */
    private onGemClicked(gem: Gem): void {
        if (this.isProcessing) return;
        
        if (!this.selectedGem) {
            // Chọn gem đầu tiên
            this.selectedGem = gem;
            gem.select();
        } else if (this.selectedGem === gem) {
            // Bỏ chọn gem hiện tại
            gem.deselect();
            this.selectedGem = null;
        } else if (this.areAdjacent(this.selectedGem, gem)) {
            // Swap hai gems liền kề
            this.attemptSwap(this.selectedGem, gem);
        } else {
            // Chọn gem mới
            this.selectedGem.deselect();
            this.selectedGem = gem;
            gem.select();
        }
    }
    
    /**
     * Kiểm tra hai gems có liền kề không
     */
    private areAdjacent(gem1: Gem, gem2: Gem): boolean {
        const dx = Math.abs(gem1.gridX - gem2.gridX);
        const dy = Math.abs(gem1.gridY - gem2.gridY);
        
        return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
    }
    
    /**
     * Thử swap hai gems
     */
    private async attemptSwap(gem1: Gem, gem2: Gem): Promise<void> {
        this.isProcessing = true;
        
        // Deselect gems
        gem1.deselect();
        this.selectedGem = null;
        
        // Swap positions trong grid
        const tempRow = gem1.gridY;
        const tempCol = gem1.gridX;
        
        this.grid[gem1.gridY][gem1.gridX] = gem2;
        this.grid[gem2.gridY][gem2.gridX] = gem1;
        
        // Calculate new world positions
        const gem1NewX = this.startX + gem2.gridX * this.cellSize + this.cellSize / 2;
        const gem1NewY = this.startY + gem2.gridY * this.cellSize + this.cellSize / 2;
        const gem2NewX = this.startX + tempCol * this.cellSize + this.cellSize / 2;
        const gem2NewY = this.startY + tempRow * this.cellSize + this.cellSize / 2;
        
        // Animate swap
        await Promise.all([
            gem1.moveTo(gem2.gridX, gem2.gridY, gem1NewX, gem1NewY),
            gem2.moveTo(tempCol, tempRow, gem2NewX, gem2NewY)
        ]);
        
        // Check for matches
        const matches = this.findMatches();
        
        if (matches.length > 0) {
            // Valid move - emit move event và process matches
            this.scene.events.emit('move-made');
            await this.processMatches(matches, false);
        } else {
            // Invalid move - swap back
            await this.swapBack(gem1, gem2);
        }
        
        this.isProcessing = false;
    }
    
    /**
     * Swap back nếu move không hợp lệ
     */
    private async swapBack(gem1: Gem, gem2: Gem): Promise<void> {
        // Swap lại positions trong grid
        const tempRow = gem1.gridY;
        const tempCol = gem1.gridX;
        
        this.grid[gem1.gridY][gem1.gridX] = gem2;
        this.grid[gem2.gridY][gem2.gridX] = gem1;
        
        // Calculate original positions
        const gem1OrigX = this.startX + gem2.gridX * this.cellSize + this.cellSize / 2;
        const gem1OrigY = this.startY + gem2.gridY * this.cellSize + this.cellSize / 2;
        const gem2OrigX = this.startX + tempCol * this.cellSize + this.cellSize / 2;
        const gem2OrigY = this.startY + tempRow * this.cellSize + this.cellSize / 2;
        
        // Animate back
        await Promise.all([
            gem1.moveTo(gem2.gridX, gem2.gridY, gem1OrigX, gem1OrigY),
            gem2.moveTo(tempCol, tempRow, gem2OrigX, gem2OrigY)
        ]);
    }
    
    /**
     * Tìm tất cả matches trên board
     */
    private findMatches(): Gem[] {
        const matches: Set<Gem> = new Set();
        
        // Check horizontal matches
        for (let row = 0; row < this.gridSize; row++) {
            let count = 1;
            let currentType = this.grid[row][0]?.gemType;
            
            for (let col = 1; col < this.gridSize; col++) {
                if (this.grid[row][col]?.gemType === currentType) {
                    count++;
                } else {
                    if (count >= 3) {
                        for (let i = col - count; i < col; i++) {
                            matches.add(this.grid[row][i]!);
                        }
                    }
                    count = 1;
                    currentType = this.grid[row][col]?.gemType;
                }
            }
            
            // Check end of row
            if (count >= 3) {
                for (let i = this.gridSize - count; i < this.gridSize; i++) {
                    matches.add(this.grid[row][i]!);
                }
            }
        }
        
        // Check vertical matches
        for (let col = 0; col < this.gridSize; col++) {
            let count = 1;
            let currentType = this.grid[0][col]?.gemType;
            
            for (let row = 1; row < this.gridSize; row++) {
                if (this.grid[row][col]?.gemType === currentType) {
                    count++;
                } else {
                    if (count >= 3) {
                        for (let i = row - count; i < row; i++) {
                            matches.add(this.grid[i][col]!);
                        }
                    }
                    count = 1;
                    currentType = this.grid[row][col]?.gemType;
                }
            }
            
            // Check end of column
            if (count >= 3) {
                for (let i = this.gridSize - count; i < this.gridSize; i++) {
                    matches.add(this.grid[i][col]!);
                }
            }
        }
        
        return Array.from(matches);
    }
    
    /**
     * Xử lý matches
     */
    private async processMatches(matches: Gem[], isCombo: boolean = false): Promise<void> {
        // Emit matches processed event for scoring
        this.scene.events.emit('matches-processed', { matches, isCombo });

        // Play match animations
        await Promise.all(matches.map(gem => gem.playMatchAnimation()));

        // Remove gems from grid
        matches.forEach(gem => {
            this.grid[gem.gridY][gem.gridX] = null;
            gem.destroy();
        });

        // Apply gravity
        await this.applyGravity();

        // Refill grid
        await this.refillGrid();

        // Check for new matches (cascade)
        const newMatches = this.findMatches();
        if (newMatches.length > 0) {
            await this.processMatches(newMatches, true); // Cascade = combo
        } else {
            // No more matches - break combo
            this.scene.events.emit('combo-broken');
        }
    }
    
    /**
     * Áp dụng gravity - gems rơi xuống
     */
    private async applyGravity(): Promise<void> {
        const movePromises: Promise<void>[] = [];
        
        for (let col = 0; col < this.gridSize; col++) {
            let writeIndex = this.gridSize - 1;
            
            for (let row = this.gridSize - 1; row >= 0; row--) {
                if (this.grid[row][col] !== null) {
                    if (row !== writeIndex) {
                        const gem = this.grid[row][col]!;
                        this.grid[row][col] = null;
                        this.grid[writeIndex][col] = gem;
                        
                        const newY = this.startY + writeIndex * this.cellSize + this.cellSize / 2;
                        movePromises.push(gem.moveTo(col, writeIndex, gem.x, newY));
                    }
                    writeIndex--;
                }
            }
        }
        
        await Promise.all(movePromises);
    }
    
    /**
     * Refill grid với gems mới với staggered animation
     */
    private async refillGrid(): Promise<void> {
        const newGems: Promise<void>[] = [];
        let delay = 0;

        for (let col = 0; col < this.gridSize; col++) {
            let gemsInColumn = 0;

            for (let row = 0; row < this.gridSize; row++) {
                if (this.grid[row][col] === null) {
                    const gemType = Phaser.Math.Between(0, 6);
                    const x = this.startX + col * this.cellSize + this.cellSize / 2;
                    const startY = this.startY - (gemsInColumn + 1) * this.cellSize;
                    const endY = this.startY + row * this.cellSize + this.cellSize / 2;

                    const gem = new Gem(this.scene, x, startY, gemType, col, row);
                    this.grid[row][col] = gem;

                    // Staggered timing for more natural feel
                    const gemDelay = delay + (gemsInColumn * 50);
                    const fallDuration = 300 + (gemsInColumn * 100);

                    newGems.push(new Promise<void>((resolve) => {
                        this.scene.time.delayedCall(gemDelay, () => {
                            gem.moveTo(col, row, x, endY, fallDuration).then(resolve);
                        });
                    }));

                    gemsInColumn++;
                }
            }

            delay += 30; // Slight delay between columns
        }

        await Promise.all(newGems);
    }

    /**
     * Show hint for possible moves
     */
    private showHint(): void {
        if (this.isProcessing) return;

        // Find a possible move
        const possibleMove = this.findPossibleMove();
        if (possibleMove) {
            const { gem1, gem2 } = possibleMove;

            // Subtle hint animation
            [gem1, gem2].forEach(gem => {
                this.scene.tweens.add({
                    targets: gem,
                    alpha: 0.7,
                    duration: 300,
                    yoyo: true,
                    repeat: 2,
                    ease: 'Sine.easeInOut'
                });
            });
        }
    }

    /**
     * Find a possible move on the board
     */
    private findPossibleMove(): { gem1: Gem, gem2: Gem } | null {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const gem1 = this.grid[row][col];
                if (!gem1) continue;

                // Check adjacent positions
                const directions = [
                    { dr: 0, dc: 1 },  // right
                    { dr: 1, dc: 0 },  // down
                ];

                for (const dir of directions) {
                    const newRow = row + dir.dr;
                    const newCol = col + dir.dc;

                    if (newRow < this.gridSize && newCol < this.gridSize) {
                        const gem2 = this.grid[newRow][newCol];
                        if (gem2 && this.wouldCreateMatchAfterSwap(gem1, gem2)) {
                            return { gem1, gem2 };
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * Check if swapping two gems would create a match
     */
    private wouldCreateMatchAfterSwap(gem1: Gem, gem2: Gem): boolean {
        // Temporarily swap gem types
        const temp = gem1.gemType;
        gem1.gemType = gem2.gemType;
        gem2.gemType = temp;

        // Check for matches
        const hasMatch = this.wouldCreateMatch(gem1.gridY, gem1.gridX, gem1.gemType) ||
                         this.wouldCreateMatch(gem2.gridY, gem2.gridX, gem2.gemType);

        // Swap back
        gem1.gemType = gem2.gemType;
        gem2.gemType = temp;

        return hasMatch;
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('gem-clicked', this.onGemClicked, this);

        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                if (this.grid[row][col]) {
                    this.grid[row][col]!.destroy();
                }
            }
        }
    }
}
