import { Gem, SpecialGemType } from '../objects/Gem';
import { ScoreManager } from './ScoreManager';

/**
 * Interface cho match information
 */
export interface MatchInfo {
    gems: Gem[];
    pattern: MatchPattern;
    centerGem?: Gem; // Gem sẽ được convert thành special
}

/**
 * Enum cho các pattern types
 */
export enum MatchPattern {
    NORMAL_3 = 'normal_3',
    NORMAL_4 = 'normal_4',
    NORMAL_5_PLUS = 'normal_5_plus',
    L_SHAPE = 'l_shape',
    T_SHAPE = 't_shape'
}

/**
 * Class quản lý game board và logic chính
 */
export class GameBoard {
    private scene: Phaser.Scene;
    private grid: (Gem | null)[][];
    private gridSize: number = 8;
    private cellSize: number = 80;
    private startX: number;
    private startY: number;
    private selectedGem: Gem | null = null;
    private isProcessing: boolean = false;
    private scoreManager: ScoreManager;
    
    constructor(scene: Phaser.Scene, scoreManager: ScoreManager) {
        this.scene = scene;
        this.scoreManager = scoreManager;
        this.grid = [];

        // Tính toán vị trí trung tâm
        this.startX = (1024 - (this.gridSize * this.cellSize)) / 2;
        this.startY = (768 - (this.gridSize * this.cellSize)) / 2;

        this.initializeGrid();
        this.setupEventListeners();
    }
    
    /**
     * Khởi tạo grid với gems ngẫu nhiên
     */
    private initializeGrid(): void {
        // Tạo background cells
        this.createGridBackground();
        
        // Khởi tạo array 2D
        for (let row = 0; row < this.gridSize; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridSize; col++) {
                this.grid[row][col] = null;
            }
        }
        
        // Fill với gems ngẫu nhiên (tránh matches ban đầu)
        this.fillGridWithoutMatches();
    }
    
    /**
     * Tạo background cho grid
     */
    private createGridBackground(): void {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const x = this.startX + col * this.cellSize + this.cellSize / 2;
                const y = this.startY + row * this.cellSize + this.cellSize / 2;
                
                const cell = this.scene.add.sprite(x, y, 'grid_cell');
                cell.setDepth(-1);
            }
        }
    }
    
    /**
     * Fill grid với gems mà không tạo matches ban đầu
     */
    private fillGridWithoutMatches(): void {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                let gemType: number;
                let attempts = 0;
                
                do {
                    gemType = Phaser.Math.Between(0, 6); // 7 loại gem
                    attempts++;
                } while (this.wouldCreateMatch(row, col, gemType) && attempts < 10);
                
                this.createGem(row, col, gemType);
            }
        }
    }
    
    /**
     * Kiểm tra xem việc đặt gem type tại vị trí có tạo match không
     */
    private wouldCreateMatch(row: number, col: number, gemType: number): boolean {
        // Kiểm tra horizontal
        let horizontalCount = 1;
        
        // Check left
        for (let c = col - 1; c >= 0; c--) {
            if (this.grid[row][c] && this.grid[row][c]!.gemType === gemType) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        // Check right
        for (let c = col + 1; c < this.gridSize; c++) {
            if (this.grid[row][c] && this.grid[row][c]!.gemType === gemType) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        if (horizontalCount >= 3) return true;
        
        // Kiểm tra vertical
        let verticalCount = 1;
        
        // Check up
        for (let r = row - 1; r >= 0; r--) {
            if (this.grid[r][col] && this.grid[r][col]!.gemType === gemType) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        // Check down
        for (let r = row + 1; r < this.gridSize; r++) {
            if (this.grid[r][col] && this.grid[r][col]!.gemType === gemType) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        return verticalCount >= 3;
    }
    
    /**
     * Tạo gem tại vị trí grid
     */
    private createGem(row: number, col: number, gemType: number): void {
        const x = this.startX + col * this.cellSize + this.cellSize / 2;
        const y = this.startY + row * this.cellSize + this.cellSize / 2;
        
        const gem = new Gem(this.scene, x, y, gemType, col, row);
        this.grid[row][col] = gem;
    }
    
    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('gem-clicked', this.onGemClicked, this);
        this.scene.events.on('special-gem-activated', this.onSpecialGemActivated, this);

        // Show hint after inactivity
        this.scene.time.addEvent({
            delay: 5000,
            callback: this.showHint,
            callbackScope: this,
            loop: true
        });
    }
    
    /**
     * Xử lý khi gem được click
     */
    private onGemClicked(gem: Gem): void {
        if (this.isProcessing) return;
        
        if (!this.selectedGem) {
            // Chọn gem đầu tiên
            this.selectedGem = gem;
            gem.select();
        } else if (this.selectedGem === gem) {
            // Bỏ chọn gem hiện tại
            gem.deselect();
            this.selectedGem = null;
        } else if (this.areAdjacent(this.selectedGem, gem)) {
            // Swap hai gems liền kề
            this.attemptSwap(this.selectedGem, gem);
        } else {
            // Chọn gem mới
            this.selectedGem.deselect();
            this.selectedGem = gem;
            gem.select();
        }
    }
    
    /**
     * Kiểm tra hai gems có liền kề không
     */
    private areAdjacent(gem1: Gem, gem2: Gem): boolean {
        const dx = Math.abs(gem1.gridX - gem2.gridX);
        const dy = Math.abs(gem1.gridY - gem2.gridY);
        
        return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
    }

    /**
     * Xử lý khi special gem được activated
     */
    private async onSpecialGemActivated(data: any): Promise<void> {
        if (this.isProcessing) return;

        const { gem, specialType } = data;
        this.isProcessing = true;

        // Get gems to be affected by special power
        const affectedGems = this.getAffectedGemsBySpecialPower(gem, specialType);

        if (affectedGems.length > 0) {
            // Emit special gem activated event for effects
            this.scene.events.emit('special-gem-activated', { gem, specialType });

            // Create match info for affected gems
            const matchInfo: MatchInfo = {
                gems: affectedGems,
                pattern: MatchPattern.NORMAL_3, // Special activation doesn't create new special gems
                centerGem: undefined
            };

            // Process the special activation
            await this.processMatches([matchInfo], false);
        }

        this.isProcessing = false;
    }

    /**
     * Get gems affected by special power
     */
    private getAffectedGemsBySpecialPower(gem: Gem, specialType: SpecialGemType): Gem[] {
        const affected: Gem[] = [];

        switch (specialType) {
            case SpecialGemType.STRIPED_HORIZONTAL:
                // Clear entire row
                for (let col = 0; col < this.gridSize; col++) {
                    const targetGem = this.grid[gem.gridY][col];
                    if (targetGem && !targetGem.isMatched) {
                        affected.push(targetGem);
                    }
                }
                break;

            case SpecialGemType.STRIPED_VERTICAL:
                // Clear entire column
                for (let row = 0; row < this.gridSize; row++) {
                    const targetGem = this.grid[row][gem.gridX];
                    if (targetGem && !targetGem.isMatched) {
                        affected.push(targetGem);
                    }
                }
                break;

            case SpecialGemType.WRAPPED:
                // Clear 3x3 area around gem
                for (let row = gem.gridY - 1; row <= gem.gridY + 1; row++) {
                    for (let col = gem.gridX - 1; col <= gem.gridX + 1; col++) {
                        if (row >= 0 && row < this.gridSize && col >= 0 && col < this.gridSize) {
                            const targetGem = this.grid[row][col];
                            if (targetGem && !targetGem.isMatched) {
                                affected.push(targetGem);
                            }
                        }
                    }
                }
                break;

            case SpecialGemType.COLOR_BOMB:
                // Clear all gems of same color as the gem that was swapped with color bomb
                // For now, clear all gems of the same type as a random gem on board
                const targetType = this.getRandomGemTypeOnBoard();
                if (targetType !== -1) {
                    for (let row = 0; row < this.gridSize; row++) {
                        for (let col = 0; col < this.gridSize; col++) {
                            const targetGem = this.grid[row][col];
                            if (targetGem && targetGem.gemType === targetType && !targetGem.isMatched) {
                                affected.push(targetGem);
                            }
                        }
                    }
                }
                break;
        }

        return affected;
    }

    /**
     * Get random gem type on board (for color bomb)
     */
    private getRandomGemTypeOnBoard(): number {
        const availableTypes: number[] = [];

        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const gem = this.grid[row][col];
                if (gem && !gem.isSpecial && !availableTypes.includes(gem.gemType)) {
                    availableTypes.push(gem.gemType);
                }
            }
        }

        return availableTypes.length > 0
            ? availableTypes[Math.floor(Math.random() * availableTypes.length)]
            : -1;
    }

    /**
     * Thử swap hai gems
     */
    private async attemptSwap(gem1: Gem, gem2: Gem): Promise<void> {
        this.isProcessing = true;
        
        // Deselect gems
        gem1.deselect();
        this.selectedGem = null;
        
        // Swap positions trong grid
        const tempRow = gem1.gridY;
        const tempCol = gem1.gridX;
        
        this.grid[gem1.gridY][gem1.gridX] = gem2;
        this.grid[gem2.gridY][gem2.gridX] = gem1;
        
        // Calculate new world positions
        const gem1NewX = this.startX + gem2.gridX * this.cellSize + this.cellSize / 2;
        const gem1NewY = this.startY + gem2.gridY * this.cellSize + this.cellSize / 2;
        const gem2NewX = this.startX + tempCol * this.cellSize + this.cellSize / 2;
        const gem2NewY = this.startY + tempRow * this.cellSize + this.cellSize / 2;
        
        // Animate swap
        await Promise.all([
            gem1.moveTo(gem2.gridX, gem2.gridY, gem1NewX, gem1NewY),
            gem2.moveTo(tempCol, tempRow, gem2NewX, gem2NewY)
        ]);
        
        // Check for matches
        const matchInfos = this.findMatches();

        if (matchInfos.length > 0) {
            // Valid move - emit move event và process matches
            this.scene.events.emit('move-made');
            await this.processMatches(matchInfos, false);
        } else {
            // Invalid move - swap back
            await this.swapBack(gem1, gem2);
        }
        
        this.isProcessing = false;
    }

    /**
     * Tạo special gems dựa trên match patterns
     */
    private async createSpecialGems(matchInfos: MatchInfo[]): Promise<void> {
        for (const matchInfo of matchInfos) {
            if (matchInfo.centerGem && this.shouldCreateSpecialGem(matchInfo.pattern)) {
                const specialType = this.getSpecialTypeFromPattern(matchInfo);

                // Convert center gem to special gem
                matchInfo.centerGem.makeSpecial(specialType);

                // Remove center gem from the gems to be destroyed
                const centerIndex = matchInfo.gems.indexOf(matchInfo.centerGem);
                if (centerIndex > -1) {
                    matchInfo.gems.splice(centerIndex, 1);
                }

                // Add visual feedback
                this.scene.events.emit('special-gem-created', {
                    gem: matchInfo.centerGem,
                    specialType,
                    pattern: matchInfo.pattern
                });
            }
        }
    }

    /**
     * Check if pattern should create special gem
     */
    private shouldCreateSpecialGem(pattern: MatchPattern): boolean {
        return pattern !== MatchPattern.NORMAL_3;
    }

    /**
     * Get special gem type từ match pattern
     */
    private getSpecialTypeFromPattern(matchInfo: MatchInfo): SpecialGemType {
        switch (matchInfo.pattern) {
            case MatchPattern.NORMAL_4:
                // Determine direction based on match orientation
                return this.isHorizontalMatch(matchInfo.gems)
                    ? SpecialGemType.STRIPED_HORIZONTAL
                    : SpecialGemType.STRIPED_VERTICAL;

            case MatchPattern.NORMAL_5_PLUS:
                return SpecialGemType.COLOR_BOMB;

            case MatchPattern.L_SHAPE:
            case MatchPattern.T_SHAPE:
                return SpecialGemType.WRAPPED;

            default:
                return SpecialGemType.NONE;
        }
    }

    /**
     * Check if match is horizontal
     */
    private isHorizontalMatch(gems: Gem[]): boolean {
        if (gems.length < 2) return false;

        // Check if all gems are in same row
        const firstRow = gems[0].gridY;
        return gems.every(gem => gem.gridY === firstRow);
    }

    /**
     * Swap back nếu move không hợp lệ
     */
    private async swapBack(gem1: Gem, gem2: Gem): Promise<void> {
        // Swap lại positions trong grid
        const tempRow = gem1.gridY;
        const tempCol = gem1.gridX;
        
        this.grid[gem1.gridY][gem1.gridX] = gem2;
        this.grid[gem2.gridY][gem2.gridX] = gem1;
        
        // Calculate original positions
        const gem1OrigX = this.startX + gem2.gridX * this.cellSize + this.cellSize / 2;
        const gem1OrigY = this.startY + gem2.gridY * this.cellSize + this.cellSize / 2;
        const gem2OrigX = this.startX + tempCol * this.cellSize + this.cellSize / 2;
        const gem2OrigY = this.startY + tempRow * this.cellSize + this.cellSize / 2;
        
        // Animate back
        await Promise.all([
            gem1.moveTo(gem2.gridX, gem2.gridY, gem1OrigX, gem1OrigY),
            gem2.moveTo(tempCol, tempRow, gem2OrigX, gem2OrigY)
        ]);
    }
    
    /**
     * Tìm tất cả matches trên board với enhanced pattern detection
     */
    private findMatches(): MatchInfo[] {
        const matchInfos: MatchInfo[] = [];
        const processedGems: Set<Gem> = new Set();

        // First pass: Find all horizontal and vertical matches
        const horizontalMatches = this.findHorizontalMatches();
        const verticalMatches = this.findVerticalMatches();

        // Combine and analyze patterns
        const allMatches = [...horizontalMatches, ...verticalMatches];

        // Check for L/T shapes by finding intersections
        const shapeMatches = this.findShapeMatches(horizontalMatches, verticalMatches);

        // Add shape matches first (higher priority)
        matchInfos.push(...shapeMatches);
        shapeMatches.forEach(match => {
            match.gems.forEach(gem => processedGems.add(gem));
        });

        // Add remaining line matches
        allMatches.forEach(match => {
            // Skip if gems already processed in shape matches
            if (!match.gems.some(gem => processedGems.has(gem))) {
                matchInfos.push(match);
            }
        });

        return matchInfos;
    }

    /**
     * Tìm horizontal matches
     */
    private findHorizontalMatches(): MatchInfo[] {
        const matches: MatchInfo[] = [];

        for (let row = 0; row < this.gridSize; row++) {
            let count = 1;
            let currentType = this.grid[row][0]?.gemType;
            let startCol = 0;

            for (let col = 1; col <= this.gridSize; col++) {
                const gem = col < this.gridSize ? this.grid[row][col] : null;

                if (gem?.gemType === currentType) {
                    count++;
                } else {
                    if (count >= 3) {
                        const matchGems: Gem[] = [];
                        for (let i = startCol; i < startCol + count; i++) {
                            matchGems.push(this.grid[row][i]!);
                        }

                        const pattern = this.getLinePattern(count);
                        const centerIndex = Math.floor(count / 2);
                        const centerGem = matchGems[centerIndex];

                        matches.push({
                            gems: matchGems,
                            pattern,
                            centerGem
                        });
                    }

                    count = 1;
                    currentType = gem?.gemType;
                    startCol = col;
                }
            }
        }

        return matches;
    }

    /**
     * Tìm vertical matches
     */
    private findVerticalMatches(): MatchInfo[] {
        const matches: MatchInfo[] = [];

        for (let col = 0; col < this.gridSize; col++) {
            let count = 1;
            let currentType = this.grid[0][col]?.gemType;
            let startRow = 0;

            for (let row = 1; row <= this.gridSize; row++) {
                const gem = row < this.gridSize ? this.grid[row][col] : null;

                if (gem?.gemType === currentType) {
                    count++;
                } else {
                    if (count >= 3) {
                        const matchGems: Gem[] = [];
                        for (let i = startRow; i < startRow + count; i++) {
                            matchGems.push(this.grid[i][col]!);
                        }

                        const pattern = this.getLinePattern(count);
                        const centerIndex = Math.floor(count / 2);
                        const centerGem = matchGems[centerIndex];

                        matches.push({
                            gems: matchGems,
                            pattern,
                            centerGem
                        });
                    }

                    count = 1;
                    currentType = gem?.gemType;
                    startRow = row;
                }
            }
        }

        return matches;
    }

    /**
     * Tìm L/T shape matches
     */
    private findShapeMatches(horizontalMatches: MatchInfo[], verticalMatches: MatchInfo[]): MatchInfo[] {
        const shapeMatches: MatchInfo[] = [];

        // Check intersections between horizontal and vertical matches
        for (const hMatch of horizontalMatches) {
            for (const vMatch of verticalMatches) {
                const intersection = this.findIntersection(hMatch, vMatch);
                if (intersection) {
                    // Combine gems from both matches
                    const allGems = new Set([...hMatch.gems, ...vMatch.gems]);
                    const pattern = this.determineShapePattern(hMatch, vMatch);

                    shapeMatches.push({
                        gems: Array.from(allGems),
                        pattern,
                        centerGem: intersection
                    });
                }
            }
        }

        return shapeMatches;
    }

    /**
     * Tìm intersection gem giữa horizontal và vertical match
     */
    private findIntersection(hMatch: MatchInfo, vMatch: MatchInfo): Gem | null {
        for (const hGem of hMatch.gems) {
            for (const vGem of vMatch.gems) {
                if (hGem.gridX === vGem.gridX && hGem.gridY === vGem.gridY) {
                    return hGem;
                }
            }
        }
        return null;
    }

    /**
     * Determine shape pattern từ horizontal và vertical matches
     */
    private determineShapePattern(hMatch: MatchInfo, vMatch: MatchInfo): MatchPattern {
        // For now, treat all shape matches as T_SHAPE
        // Could be enhanced to distinguish between L and T shapes
        return MatchPattern.T_SHAPE;
    }

    /**
     * Get pattern type dựa trên line length
     */
    private getLinePattern(count: number): MatchPattern {
        if (count === 3) return MatchPattern.NORMAL_3;
        if (count === 4) return MatchPattern.NORMAL_4;
        return MatchPattern.NORMAL_5_PLUS;
    }

    /**
     * Xử lý matches với special gem creation
     */
    private async processMatches(matchInfos: MatchInfo[], isCombo: boolean = false): Promise<void> {
        // Extract all gems to be removed
        const allGems = new Set<Gem>();
        matchInfos.forEach(matchInfo => {
            matchInfo.gems.forEach(gem => allGems.add(gem));
        });

        // Emit matches processed event for scoring
        this.scene.events.emit('matches-processed', { matches: Array.from(allGems), isCombo });

        // Create special gems before removing matched gems
        await this.createSpecialGems(matchInfos);

        // Play match animations for gems that will be removed
        const gemsToRemove = Array.from(allGems).filter(gem => !gem.isSpecial);
        await Promise.all(gemsToRemove.map(gem => gem.playMatchAnimation()));

        // Remove gems from grid (except newly created special gems)
        gemsToRemove.forEach(gem => {
            this.grid[gem.gridY][gem.gridX] = null;
            gem.destroy();
        });

        // Apply gravity
        await this.applyGravity();

        // Refill grid
        await this.refillGrid();

        // Check for new matches (cascade)
        const newMatchInfos = this.findMatches();
        if (newMatchInfos.length > 0) {
            await this.processMatches(newMatchInfos, true); // Cascade = combo
        } else {
            // No more matches - break combo
            this.scene.events.emit('combo-broken');
        }
    }
    
    /**
     * Áp dụng gravity - gems rơi xuống
     */
    private async applyGravity(): Promise<void> {
        const movePromises: Promise<void>[] = [];
        
        for (let col = 0; col < this.gridSize; col++) {
            let writeIndex = this.gridSize - 1;
            
            for (let row = this.gridSize - 1; row >= 0; row--) {
                if (this.grid[row][col] !== null) {
                    if (row !== writeIndex) {
                        const gem = this.grid[row][col]!;
                        this.grid[row][col] = null;
                        this.grid[writeIndex][col] = gem;
                        
                        const newY = this.startY + writeIndex * this.cellSize + this.cellSize / 2;
                        movePromises.push(gem.moveTo(col, writeIndex, gem.x, newY));
                    }
                    writeIndex--;
                }
            }
        }
        
        await Promise.all(movePromises);
    }
    
    /**
     * Refill grid với gems mới với staggered animation
     */
    private async refillGrid(): Promise<void> {
        const newGems: Promise<void>[] = [];
        let delay = 0;

        for (let col = 0; col < this.gridSize; col++) {
            let gemsInColumn = 0;

            for (let row = 0; row < this.gridSize; row++) {
                if (this.grid[row][col] === null) {
                    const gemType = Phaser.Math.Between(0, 6);
                    const x = this.startX + col * this.cellSize + this.cellSize / 2;
                    const startY = this.startY - (gemsInColumn + 1) * this.cellSize;
                    const endY = this.startY + row * this.cellSize + this.cellSize / 2;

                    const gem = new Gem(this.scene, x, startY, gemType, col, row);
                    this.grid[row][col] = gem;

                    // Staggered timing for more natural feel
                    const gemDelay = delay + (gemsInColumn * 50);
                    const fallDuration = 300 + (gemsInColumn * 100);

                    newGems.push(new Promise<void>((resolve) => {
                        this.scene.time.delayedCall(gemDelay, () => {
                            gem.moveTo(col, row, x, endY, fallDuration).then(resolve);
                        });
                    }));

                    gemsInColumn++;
                }
            }

            delay += 30; // Slight delay between columns
        }

        await Promise.all(newGems);
    }

    /**
     * Show hint for possible moves
     */
    private showHint(): void {
        if (this.isProcessing) return;

        // Find a possible move
        const possibleMove = this.findPossibleMove();
        if (possibleMove) {
            const { gem1, gem2 } = possibleMove;

            // Subtle hint animation
            [gem1, gem2].forEach(gem => {
                this.scene.tweens.add({
                    targets: gem,
                    alpha: 0.7,
                    duration: 300,
                    yoyo: true,
                    repeat: 2,
                    ease: 'Sine.easeInOut'
                });
            });
        }
    }

    /**
     * Find a possible move on the board
     */
    private findPossibleMove(): { gem1: Gem, gem2: Gem } | null {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const gem1 = this.grid[row][col];
                if (!gem1) continue;

                // Check adjacent positions
                const directions = [
                    { dr: 0, dc: 1 },  // right
                    { dr: 1, dc: 0 },  // down
                ];

                for (const dir of directions) {
                    const newRow = row + dir.dr;
                    const newCol = col + dir.dc;

                    if (newRow < this.gridSize && newCol < this.gridSize) {
                        const gem2 = this.grid[newRow][newCol];
                        if (gem2 && this.wouldCreateMatchAfterSwap(gem1, gem2)) {
                            return { gem1, gem2 };
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * Check if swapping two gems would create a match
     */
    private wouldCreateMatchAfterSwap(gem1: Gem, gem2: Gem): boolean {
        // Temporarily swap gem types
        const temp = gem1.gemType;
        gem1.gemType = gem2.gemType;
        gem2.gemType = temp;

        // Check for matches
        const hasMatch = this.wouldCreateMatch(gem1.gridY, gem1.gridX, gem1.gemType) ||
                         this.wouldCreateMatch(gem2.gridY, gem2.gridX, gem2.gemType);

        // Swap back
        gem1.gemType = gem2.gemType;
        gem2.gemType = temp;

        return hasMatch;
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('gem-clicked', this.onGemClicked, this);
        this.scene.events.off('special-gem-activated', this.onSpecialGemActivated, this);

        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                if (this.grid[row][col]) {
                    this.grid[row][col]!.destroy();
                }
            }
        }
    }
}
