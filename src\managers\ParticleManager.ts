/**
 * Class quản lý các particle effects trong game
 */
export class ParticleManager {
    private scene: Phaser.Scene;
    private backgroundParticles?: Phaser.GameObjects.Particles.ParticleEmitter;
    private activeTrails: Map<string, Phaser.GameObjects.Particles.ParticleEmitter> = new Map();
    
    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.createBackgroundParticles();
        this.setupEventListeners();
    }
    
    /**
     * Tạo background ambient particles
     */
    private createBackgroundParticles(): void {
        // Floating sparkles in background
        this.backgroundParticles = this.scene.add.particles(0, 0, 'particle', {
            x: { min: 0, max: 1024 },
            y: { min: 0, max: 768 },
            speed: { min: 10, max: 30 },
            scale: { min: 0.1, max: 0.3 },
            alpha: { min: 0.2, max: 0.6 },
            lifespan: { min: 3000, max: 8000 },
            frequency: 200,
            quantity: 1,
            tint: [0xffffff, 0xffff88, 0x88ffff, 0xff88ff],
            blendMode: 'ADD',
            angle: { min: 0, max: 360 },
            gravityY: -20
        });
        
        this.backgroundParticles.setDepth(-10); // Behind everything
    }
    
    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('gem-moving', this.createGemTrail, this);
        this.scene.events.on('combo-achieved', this.createComboEffect, this);
        this.scene.events.on('level-completed', this.createCelebrationEffect, this);
    }
    
    /**
     * Tạo trail effect khi gem di chuyển
     */
    private createGemTrail(gem: any): void {
        const trailKey = `trail_${gem.gridX}_${gem.gridY}`;
        
        // Remove existing trail if any
        if (this.activeTrails.has(trailKey)) {
            this.activeTrails.get(trailKey)?.destroy();
            this.activeTrails.delete(trailKey);
        }
        
        // Create new trail
        const trail = this.scene.add.particles(gem.x, gem.y, 'particle', {
            follow: gem,
            speed: { min: 20, max: 50 },
            scale: { start: 0.3, end: 0 },
            alpha: { start: 0.8, end: 0 },
            lifespan: 300,
            frequency: 50,
            quantity: 2,
            tint: this.getGemColor(gem.gemType),
            blendMode: 'ADD'
        });
        
        this.activeTrails.set(trailKey, trail);
        
        // Auto cleanup after movement
        this.scene.time.delayedCall(1000, () => {
            if (this.activeTrails.has(trailKey)) {
                this.activeTrails.get(trailKey)?.destroy();
                this.activeTrails.delete(trailKey);
            }
        });
    }
    
    /**
     * Tạo combo effect
     */
    private createComboEffect(data: { comboCount: number, x: number, y: number }): void {
        const { comboCount, x, y } = data;
        
        // Intensity increases with combo count
        const intensity = Math.min(comboCount * 2, 10);
        
        // Combo burst effect
        const comboBurst = this.scene.add.particles(x, y, 'particle', {
            speed: { min: 100, max: 200 + (intensity * 20) },
            scale: { start: 0.5 + (intensity * 0.1), end: 0 },
            alpha: { start: 1, end: 0 },
            lifespan: 800 + (intensity * 100),
            quantity: 8 + intensity,
            angle: { min: 0, max: 360 },
            tint: [0xff6600, 0xffff00, 0xff0066],
            blendMode: 'ADD'
        });
        
        // Ring expansion effect
        const ringEffect = this.scene.add.particles(x, y, 'particle', {
            speed: { min: 80, max: 120 },
            scale: { start: 0.2, end: 0.8 },
            alpha: { start: 0.8, end: 0 },
            lifespan: 600,
            quantity: 12,
            angle: { min: 0, max: 360 },
            tint: 0xffffff,
            blendMode: 'ADD',
            emitZone: { type: 'edge', source: new Phaser.Geom.Circle(0, 0, 20), quantity: 12 }
        });
        
        // Cleanup
        this.scene.time.delayedCall(1500, () => {
            comboBurst.destroy();
            ringEffect.destroy();
        });
    }
    
    /**
     * Tạo celebration effect khi hoàn thành level
     */
    private createCelebrationEffect(): void {
        const centerX = this.scene.cameras.main.centerX;
        const centerY = this.scene.cameras.main.centerY;
        
        // Fireworks effect
        for (let i = 0; i < 5; i++) {
            this.scene.time.delayedCall(i * 300, () => {
                const x = centerX + Phaser.Math.Between(-200, 200);
                const y = centerY + Phaser.Math.Between(-100, 100);
                
                const firework = this.scene.add.particles(x, y, 'particle', {
                    speed: { min: 150, max: 300 },
                    scale: { start: 0.8, end: 0 },
                    alpha: { start: 1, end: 0 },
                    lifespan: 1200,
                    quantity: 20,
                    angle: { min: 0, max: 360 },
                    tint: [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff],
                    blendMode: 'ADD',
                    gravityY: 100
                });
                
                this.scene.time.delayedCall(1500, () => {
                    firework.destroy();
                });
            });
        }
        
        // Confetti rain
        const confetti = this.scene.add.particles(centerX, -50, 'particle', {
            x: { min: centerX - 300, max: centerX + 300 },
            speed: { min: 50, max: 150 },
            scale: { min: 0.3, max: 0.8 },
            alpha: { min: 0.7, max: 1 },
            lifespan: 3000,
            frequency: 50,
            quantity: 3,
            angle: { min: 70, max: 110 },
            tint: [0xff6b6b, 0x4ecdc4, 0x45b7d1, 0xf9ca24, 0xf0932b, 0xeb4d4b],
            gravityY: 200,
            bounce: 0.4
        });
        
        this.scene.time.delayedCall(4000, () => {
            confetti.destroy();
        });
    }
    
    /**
     * Tạo screen-wide flash effect
     */
    public createScreenFlash(color: number = 0xffffff, duration: number = 200): void {
        const flash = this.scene.add.rectangle(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            this.scene.cameras.main.width,
            this.scene.cameras.main.height,
            color,
            0.3
        );
        
        flash.setDepth(1000);
        
        this.scene.tweens.add({
            targets: flash,
            alpha: 0,
            duration: duration,
            ease: 'Power2.easeOut',
            onComplete: () => {
                flash.destroy();
            }
        });
    }
    
    /**
     * Tạo impact effect tại vị trí
     */
    public createImpactEffect(x: number, y: number, intensity: number = 1): void {
        const impact = this.scene.add.particles(x, y, 'particle', {
            speed: { min: 50 * intensity, max: 150 * intensity },
            scale: { start: 0.4 * intensity, end: 0 },
            alpha: { start: 1, end: 0 },
            lifespan: 400,
            quantity: Math.floor(8 * intensity),
            angle: { min: 0, max: 360 },
            tint: [0xffffff, 0xffff88],
            blendMode: 'ADD'
        });
        
        this.scene.time.delayedCall(500, () => {
            impact.destroy();
        });
    }
    
    /**
     * Get gem color for particles
     */
    private getGemColor(gemType: number): number {
        const colors = [
            0xff4444, // red
            0x4444ff, // blue  
            0x44ff44, // green
            0xffff44, // yellow
            0xff44ff, // purple
            0xff8844, // orange
            0xffffff  // white
        ];
        return colors[gemType] || 0xffffff;
    }
    
    /**
     * Pause all particle effects
     */
    public pause(): void {
        this.backgroundParticles?.pause();
        this.activeTrails.forEach(trail => trail.pause());
    }
    
    /**
     * Resume all particle effects
     */
    public resume(): void {
        this.backgroundParticles?.resume();
        this.activeTrails.forEach(trail => trail.resume());
    }
    
    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('gem-moving', this.createGemTrail, this);
        this.scene.events.off('combo-achieved', this.createComboEffect, this);
        this.scene.events.off('level-completed', this.createCelebrationEffect, this);
        
        this.backgroundParticles?.destroy();
        this.activeTrails.forEach(trail => trail.destroy());
        this.activeTrails.clear();
    }
}
