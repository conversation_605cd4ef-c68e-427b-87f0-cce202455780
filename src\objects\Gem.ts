/**
 * Class đại diện cho một viên gem trong game
 */
export class Gem extends Phaser.GameObjects.Sprite {
    public gemType: number;
    public gridX: number;
    public gridY: number;
    public isSelected: boolean = false;
    public isMatched: boolean = false;
    public isMoving: boolean = false;
    
    private selectionGlow?: Phaser.GameObjects.Sprite;
    
    constructor(scene: Phaser.Scene, x: number, y: number, gemType: number, gridX: number, gridY: number) {
        const gemTextures = [
            'gem_red', 'gem_blue', 'gem_green', 'gem_yellow', 
            'gem_purple', 'gem_orange', 'gem_white'
        ];
        
        super(scene, x, y, gemTextures[gemType]);
        
        this.gemType = gemType;
        this.gridX = gridX;
        this.gridY = gridY;
        
        // Thêm vào scene
        scene.add.existing(this);
        
        // Set interactive
        this.setInteractive();
        this.setupInteractions();
        
        // Scale để fit grid (80px cell, 128px image -> 0.6 scale = 76.8px)
        this.setScale(0.6);
        this.setOrigin(0.5);
    }
    
    /**
     * Setup các tương tác chuột/touch với enhanced effects
     */
    private setupInteractions(): void {
        // Enhanced hover effects
        this.on('pointerover', () => {
            if (!this.isSelected && !this.isMoving) {
                // Subtle glow effect
                this.setTint(0xdddddd);

                // Gentle scale up
                this.scene.tweens.add({
                    targets: this,
                    scaleX: 0.65,
                    scaleY: 0.65,
                    duration: 150,
                    ease: 'Power2.easeOut'
                });
            }
        });

        this.on('pointerout', () => {
            if (!this.isSelected && !this.isMoving) {
                this.clearTint();

                // Scale back to normal
                this.scene.tweens.add({
                    targets: this,
                    scaleX: 0.6,
                    scaleY: 0.6,
                    duration: 150,
                    ease: 'Power2.easeOut'
                });
            }
        });

        // Enhanced click feedback
        this.on('pointerdown', () => {
            if (!this.isMoving) {
                // Quick scale down for tactile feedback
                this.scene.tweens.add({
                    targets: this,
                    scaleX: 0.55,
                    scaleY: 0.55,
                    duration: 100,
                    ease: 'Power2.easeOut',
                    yoyo: true
                });

                this.scene.events.emit('gem-clicked', this);
            }
        });
    }
    
    /**
     * Hiển thị selection
     */
    public select(): void {
        if (this.isSelected) return;
        
        this.isSelected = true;
        
        // Tạo glow effect
        this.selectionGlow = this.scene.add.sprite(this.x, this.y, 'selection');
        this.selectionGlow.setDepth(this.depth - 1);
        
        // Enhanced pulsing animation
        this.scene.tweens.add({
            targets: this.selectionGlow,
            scaleX: 1.3,
            scaleY: 1.3,
            alpha: 0.8,
            duration: 600,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // Gem scale up with bounce
        this.scene.tweens.add({
            targets: this,
            scaleX: 0.72,
            scaleY: 0.72,
            duration: 300,
            ease: 'Elastic.easeOut'
        });
    }
    
    /**
     * Bỏ selection
     */
    public deselect(): void {
        if (!this.isSelected) return;
        
        this.isSelected = false;
        this.clearTint();
        
        // Remove glow
        if (this.selectionGlow) {
            this.scene.tweens.killTweensOf(this.selectionGlow);
            this.selectionGlow.destroy();
            this.selectionGlow = undefined;
        }
        
        // Reset scale
        this.scene.tweens.add({
            targets: this,
            scaleX: 0.6,
            scaleY: 0.6,
            duration: 200,
            ease: 'Back.easeOut'
        });
    }
    
    /**
     * Di chuyển gem đến vị trí mới với enhanced animation
     */
    public moveTo(newGridX: number, newGridY: number, worldX: number, worldY: number, duration: number = 300): Promise<void> {
        return new Promise((resolve) => {
            this.isMoving = true;
            this.gridX = newGridX;
            this.gridY = newGridY;

            // Emit gem moving event for particle trails
            this.scene.events.emit('gem-moving', this);

            // Determine animation type based on movement
            const isVerticalDrop = worldY > this.y;
            const isSwap = Math.abs(worldX - this.x) > 0 && Math.abs(worldY - this.y) > 0;

            let easeType = 'Power2.easeOut';
            let actualDuration = duration;

            if (isVerticalDrop) {
                // Falling gems get bounce effect
                easeType = 'Bounce.easeOut';
                actualDuration = Math.min(duration + 200, 600);
            } else if (isSwap) {
                // Swap animations are smoother
                easeType = 'Back.easeInOut';
                actualDuration = 250;
            }

            this.scene.tweens.add({
                targets: this,
                x: worldX,
                y: worldY,
                duration: actualDuration,
                ease: easeType,
                onComplete: () => {
                    this.isMoving = false;
                    resolve();
                }
            });
        });
    }
    
    /**
     * Enhanced animation khi gem bị match
     */
    public playMatchAnimation(): Promise<void> {
        return new Promise((resolve) => {
            this.isMatched = true;

            // Multi-stage animation for more impact
            // Stage 1: Quick scale up with rotation
            this.scene.tweens.add({
                targets: this,
                scaleX: 1.3,
                scaleY: 1.3,
                rotation: 0.2,
                duration: 150,
                ease: 'Back.easeOut',
                onComplete: () => {
                    // Stage 2: Dramatic shrink and fade with spin
                    this.scene.tweens.add({
                        targets: this,
                        scaleX: 0,
                        scaleY: 0,
                        rotation: 1.5,
                        alpha: 0,
                        duration: 200,
                        ease: 'Power3.easeIn',
                        onComplete: () => {
                            resolve();
                        }
                    });
                }
            });

            // Enhanced particle effect
            this.createMatchParticles();

            // Add screen shake for impact
            this.scene.cameras.main.shake(100, 0.01);
        });
    }
    
    /**
     * Enhanced particle effects khi match
     */
    private createMatchParticles(): void {
        // Main explosion particles
        const explosionParticles = this.scene.add.particles(this.x, this.y, 'particle', {
            speed: { min: 80, max: 200 },
            scale: { start: 0.8, end: 0 },
            lifespan: 800,
            quantity: 12,
            angle: { min: 0, max: 360 },
            alpha: { start: 1, end: 0 },
            tint: this.getGemColor()
        });

        // Secondary sparkle particles
        const sparkleParticles = this.scene.add.particles(this.x, this.y, 'particle', {
            speed: { min: 30, max: 100 },
            scale: { start: 0.3, end: 0 },
            lifespan: 1000,
            quantity: 8,
            angle: { min: 0, max: 360 },
            alpha: { start: 0.8, end: 0 },
            tint: 0xffffff,
            blendMode: 'ADD'
        });

        // Cleanup particles
        this.scene.time.delayedCall(1000, () => {
            explosionParticles.destroy();
            sparkleParticles.destroy();
        });
    }

    /**
     * Get color tint based on gem type
     */
    private getGemColor(): number {
        const colors = [
            0xff4444, // red
            0x4444ff, // blue
            0x44ff44, // green
            0xffff44, // yellow
            0xff44ff, // purple
            0xff8844, // orange
            0xffffff  // white
        ];
        return colors[this.gemType] || 0xffffff;
    }
    
    /**
     * Reset gem về trạng thái ban đầu
     */
    public reset(): void {
        this.isSelected = false;
        this.isMatched = false;
        this.isMoving = false;
        this.setAlpha(1);
        this.setScale(0.6);
        this.clearTint();
        
        if (this.selectionGlow) {
            this.selectionGlow.destroy();
            this.selectionGlow = undefined;
        }
    }
    
    /**
     * Cleanup khi destroy
     */
    public destroy(): void {
        if (this.selectionGlow) {
            this.selectionGlow.destroy();
        }
        super.destroy();
    }
}
