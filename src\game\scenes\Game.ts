import { Scene } from 'phaser';
import { GameBoard } from '../../managers/GameBoard';
import { ScoreManager } from '../../managers/ScoreManager';
import { UIManager } from '../../managers/UIManager';
import { ParticleManager } from '../../managers/ParticleManager';

export class Game extends Scene
{
    private gameBoard: GameBoard;
    private scoreManager: ScoreManager;
    private uiManager: UIManager;
    private particleManager: ParticleManager;

    constructor ()
    {
        super('Game');
    }

    create ()
    {
        // Tạo background
        this.add.image(512, 384, 'background');

        // Initialize managers
        this.scoreManager = new ScoreManager(this);
        this.uiManager = new UIManager(this);
        this.particleManager = new ParticleManager(this);

        // Create game title and instructions
        this.createGameTitle();

        // Tạo game board
        this.gameBoard = new GameBoard(this, this.scoreManager);

        // Setup input
        this.setupInput();

        // Initialize UI with current scores
        this.uiManager.updateHighScore(this.scoreManager.getHighScore());
    }

    private createGameTitle(): void {
        // Title
        const title = this.add.text(512, 50, 'Match 3 Game', {
            fontFamily: 'Arial Black',
            fontSize: '48px',
            color: '#ffff00',
            stroke: '#000000',
            strokeThickness: 6
        });
        title.setOrigin(0.5, 0);

        // Instructions
        const instructions = this.add.text(512, 700, 'Click gems to select, then click adjacent gem to swap', {
            fontFamily: 'Arial',
            fontSize: '20px',
            color: '#cccccc',
            align: 'center'
        });
        instructions.setOrigin(0.5);
    }

    private setupInput(): void {
        // ESC để về menu
        this.input.keyboard?.on('keydown-ESC', () => {
            this.scene.start('MainMenu');
        });
    }

    shutdown(): void {
        if (this.gameBoard) {
            this.gameBoard.destroy();
        }
        if (this.scoreManager) {
            this.scoreManager.destroy();
        }
        if (this.uiManager) {
            this.uiManager.destroy();
        }
        if (this.particleManager) {
            this.particleManager.destroy();
        }
    }
}
