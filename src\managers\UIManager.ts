/**
 * Class quản lý UI elements cho game
 */
export class UIManager {
    private scene: Phaser.Scene;
    private scoreText?: Phaser.GameObjects.Text;
    private levelText?: Phaser.GameObjects.Text;
    private movesText?: Phaser.GameObjects.Text;
    private targetText?: Phaser.GameObjects.Text;
    private comboText?: Phaser.GameObjects.Text;
    private highScoreText?: Phaser.GameObjects.Text;
    
    // UI Container
    private uiContainer?: Phaser.GameObjects.Container;
    
    // Style constants
    private readonly TEXT_STYLE = {
        fontSize: '24px',
        fontFamily: 'Arial, sans-serif',
        color: '#ffffff',
        stroke: '#000000',
        strokeThickness: 2
    };
    
    private readonly TITLE_STYLE = {
        fontSize: '18px',
        fontFamily: 'Arial, sans-serif',
        color: '#ffff00',
        stroke: '#000000',
        strokeThickness: 2
    };
    
    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.createUI();
        this.setupEventListeners();
    }
    
    /**
     * Tạo UI elements
     */
    private createUI(): void {
        // Create container for UI
        this.uiContainer = this.scene.add.container(0, 0);
        this.uiContainer.setDepth(1000); // Ensure UI is on top
        
        // Create background panel
        this.createUIBackground();
        
        // Create text elements
        this.createScoreUI();
        this.createLevelUI();
        this.createMovesUI();
        this.createTargetUI();
        this.createComboUI();
        this.createHighScoreUI();
    }
    
    /**
     * Tạo background cho UI panel với enhanced styling
     */
    private createUIBackground(): void {
        const panelWidth = 300;
        const panelHeight = 200;

        // Create gradient background effect
        const bg = this.scene.add.rectangle(
            panelWidth / 2,
            panelHeight / 2,
            panelWidth,
            panelHeight,
            0x1a1a2e,
            0.9
        );
        bg.setStrokeStyle(3, 0x16213e);

        // Add subtle glow effect
        const glow = this.scene.add.rectangle(
            panelWidth / 2,
            panelHeight / 2,
            panelWidth + 6,
            panelHeight + 6,
            0x0f3460,
            0.3
        );

        // Animated border glow
        this.scene.tweens.add({
            targets: glow,
            alpha: 0.1,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        this.uiContainer?.add([glow, bg]);
    }
    
    /**
     * Tạo Score UI
     */
    private createScoreUI(): void {
        const scoreLabel = this.scene.add.text(20, 20, 'SCORE:', this.TITLE_STYLE);
        this.scoreText = this.scene.add.text(20, 45, '0', this.TEXT_STYLE);
        
        this.uiContainer?.add([scoreLabel, this.scoreText]);
    }
    
    /**
     * Tạo Level UI
     */
    private createLevelUI(): void {
        const levelLabel = this.scene.add.text(20, 80, 'LEVEL:', this.TITLE_STYLE);
        this.levelText = this.scene.add.text(20, 105, '1', this.TEXT_STYLE);
        
        this.uiContainer?.add([levelLabel, this.levelText]);
    }
    
    /**
     * Tạo Moves UI
     */
    private createMovesUI(): void {
        const movesLabel = this.scene.add.text(150, 20, 'MOVES:', this.TITLE_STYLE);
        this.movesText = this.scene.add.text(150, 45, '30', this.TEXT_STYLE);
        
        this.uiContainer?.add([movesLabel, this.movesText]);
    }
    
    /**
     * Tạo Target Score UI
     */
    private createTargetUI(): void {
        const targetLabel = this.scene.add.text(150, 80, 'TARGET:', this.TITLE_STYLE);
        this.targetText = this.scene.add.text(150, 105, '1000', this.TEXT_STYLE);
        
        this.uiContainer?.add([targetLabel, this.targetText]);
    }
    
    /**
     * Tạo Combo UI
     */
    private createComboUI(): void {
        const comboLabel = this.scene.add.text(20, 140, 'COMBO:', this.TITLE_STYLE);
        this.comboText = this.scene.add.text(20, 165, 'x1.0', this.TEXT_STYLE);
        
        this.uiContainer?.add([comboLabel, this.comboText]);
    }
    
    /**
     * Tạo High Score UI
     */
    private createHighScoreUI(): void {
        const highScoreLabel = this.scene.add.text(150, 140, 'BEST:', this.TITLE_STYLE);
        this.highScoreText = this.scene.add.text(150, 165, '0', this.TEXT_STYLE);
        
        this.uiContainer?.add([highScoreLabel, this.highScoreText]);
    }
    
    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('score-updated', this.onScoreUpdated, this);
        this.scene.events.on('moves-updated', this.onMovesUpdated, this);
        this.scene.events.on('level-completed', this.onLevelCompleted, this);
        this.scene.events.on('game-over', this.onGameOver, this);
    }
    
    /**
     * Update score display
     */
    private onScoreUpdated(data: { score: number, addedScore: number, combo: number, multiplier: number }): void {
        if (this.scoreText) {
            this.scoreText.setText(data.score.toString());
            
            // Animate score increase
            this.animateScoreIncrease(data.addedScore);
        }
        
        if (this.comboText && data.combo > 0) {
            this.comboText.setText(`x${data.multiplier.toFixed(1)}`);
            this.comboText.setColor('#ff6600'); // Orange for combo
            
            // Reset color after animation
            this.scene.time.delayedCall(1000, () => {
                if (this.comboText) {
                    this.comboText.setColor('#ffffff');
                }
            });
        } else if (this.comboText) {
            this.comboText.setText('x1.0');
            this.comboText.setColor('#ffffff');
        }
    }
    
    /**
     * Animate score increase
     */
    private animateScoreIncrease(addedScore: number): void {
        if (!this.scoreText) return;
        
        // Create floating score text
        const floatingScore = this.scene.add.text(
            this.scoreText.x + 100,
            this.scoreText.y,
            `+${addedScore}`,
            {
                fontSize: '20px',
                fontFamily: 'Arial, sans-serif',
                color: '#00ff00',
                stroke: '#000000',
                strokeThickness: 2
            }
        );
        
        floatingScore.setDepth(1001);
        
        // Animate floating score
        this.scene.tweens.add({
            targets: floatingScore,
            y: floatingScore.y - 50,
            alpha: 0,
            duration: 1000,
            ease: 'Power2.easeOut',
            onComplete: () => {
                floatingScore.destroy();
            }
        });
        
        // Pulse score text
        this.scene.tweens.add({
            targets: this.scoreText,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 200,
            yoyo: true,
            ease: 'Back.easeOut'
        });
    }
    
    /**
     * Update moves display
     */
    private onMovesUpdated(movesRemaining: number): void {
        if (this.movesText) {
            this.movesText.setText(movesRemaining.toString());
            
            // Change color based on remaining moves
            if (movesRemaining <= 5) {
                this.movesText.setColor('#ff0000'); // Red for low moves
            } else if (movesRemaining <= 10) {
                this.movesText.setColor('#ffaa00'); // Orange for medium moves
            } else {
                this.movesText.setColor('#ffffff'); // White for plenty moves
            }
        }
    }
    
    /**
     * Handle level completion
     */
    private onLevelCompleted(data: { level: number, targetScore: number }): void {
        if (this.levelText) {
            this.levelText.setText(data.level.toString());
        }
        
        if (this.targetText) {
            this.targetText.setText(data.targetScore.toString());
        }
        
        // Show level complete message
        this.showLevelCompleteMessage(data.level);
    }
    
    /**
     * Show level complete message
     */
    private showLevelCompleteMessage(level: number): void {
        const message = this.scene.add.text(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            `LEVEL ${level - 1} COMPLETE!\nStarting Level ${level}`,
            {
                fontSize: '32px',
                fontFamily: 'Arial, sans-serif',
                color: '#ffff00',
                stroke: '#000000',
                strokeThickness: 3,
                align: 'center'
            }
        );
        
        message.setOrigin(0.5);
        message.setDepth(1002);
        
        // Animate message
        this.scene.tweens.add({
            targets: message,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 500,
            yoyo: true,
            onComplete: () => {
                this.scene.time.delayedCall(1500, () => {
                    message.destroy();
                });
            }
        });
    }
    
    /**
     * Handle game over
     */
    private onGameOver(data: { finalScore: number, level: number, isHighScore: boolean }): void {
        const message = data.isHighScore ? 
            `GAME OVER!\nNew High Score: ${data.finalScore}\nLevel Reached: ${data.level}` :
            `GAME OVER!\nFinal Score: ${data.finalScore}\nLevel Reached: ${data.level}`;
        
        const gameOverText = this.scene.add.text(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            message,
            {
                fontSize: '28px',
                fontFamily: 'Arial, sans-serif',
                color: data.isHighScore ? '#ffff00' : '#ff6666',
                stroke: '#000000',
                strokeThickness: 3,
                align: 'center'
            }
        );
        
        gameOverText.setOrigin(0.5);
        gameOverText.setDepth(1002);
    }
    
    /**
     * Update high score display
     */
    public updateHighScore(highScore: number): void {
        if (this.highScoreText) {
            this.highScoreText.setText(highScore.toString());
        }
    }
    
    /**
     * Reset UI to initial state
     */
    public resetUI(): void {
        if (this.scoreText) this.scoreText.setText('0');
        if (this.levelText) this.levelText.setText('1');
        if (this.movesText) this.movesText.setText('30');
        if (this.targetText) this.targetText.setText('1000');
        if (this.comboText) this.comboText.setText('x1.0');
    }
    
    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('score-updated', this.onScoreUpdated, this);
        this.scene.events.off('moves-updated', this.onMovesUpdated, this);
        this.scene.events.off('level-completed', this.onLevelCompleted, this);
        this.scene.events.off('game-over', this.onGameOver, this);
        
        if (this.uiContainer) {
            this.uiContainer.destroy();
        }
    }
}
