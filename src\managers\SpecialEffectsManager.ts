import { SpecialGemType } from '../objects/Gem';

/**
 * Manager cho special gem visual effects
 */
export class SpecialEffectsManager {
    private scene: Phaser.Scene;
    private activeEffects: Map<string, Phaser.GameObjects.Container> = new Map();
    
    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.setupEventListeners();
    }
    
    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('special-gem-created', this.onSpecialGemCreated, this);
        this.scene.events.on('special-gem-activated', this.onSpecialGemActivated, this);
    }
    
    /**
     * Handle special gem creation
     */
    private onSpecialGemCreated(data: any): void {
        const { gem, specialType, pattern } = data;
        
        // Enhanced creation effect
        this.playCreationEffect(gem, specialType);
        
        // Add special glow
        this.addSpecialGlow(gem, specialType);
    }
    
    /**
     * Handle special gem activation
     */
    private onSpecialGemActivated(data: any): void {
        const { gem, specialType } = data;
        
        // Play activation effect
        this.playActivationEffect(gem, specialType);
    }
    
    /**
     * Play creation effect khi special gem được tạo
     */
    private playCreationEffect(gem: any, specialType: SpecialGemType): void {
        // Burst effect
        const burstParticles = this.scene.add.particles(gem.x, gem.y, 'particle', {
            speed: { min: 100, max: 200 },
            scale: { start: 0.6, end: 0 },
            alpha: { start: 1, end: 0 },
            lifespan: 600,
            quantity: 15,
            angle: { min: 0, max: 360 },
            tint: this.getSpecialColor(specialType),
            blendMode: 'ADD'
        });
        
        // Ring expansion
        const ring = this.scene.add.graphics();
        ring.lineStyle(4, this.getSpecialColor(specialType), 1);
        ring.strokeCircle(gem.x, gem.y, 10);
        ring.setBlendMode(Phaser.BlendModes.ADD);
        
        this.scene.tweens.add({
            targets: ring,
            scaleX: 3,
            scaleY: 3,
            alpha: 0,
            duration: 500,
            ease: 'Power2.easeOut',
            onComplete: () => ring.destroy()
        });
        
        // Screen flash
        this.scene.cameras.main.flash(200, 255, 255, 255, false, 0.3);
        
        // Cleanup particles
        this.scene.time.delayedCall(800, () => {
            burstParticles.destroy();
        });
    }
    
    /**
     * Add special glow effect
     */
    private addSpecialGlow(gem: any, specialType: SpecialGemType): void {
        const glowKey = `glow_${gem.gridX}_${gem.gridY}`;
        
        // Remove existing glow
        if (this.activeEffects.has(glowKey)) {
            this.activeEffects.get(glowKey)?.destroy();
            this.activeEffects.delete(glowKey);
        }
        
        // Create glow container
        const glowContainer = this.scene.add.container(gem.x, gem.y);
        glowContainer.setDepth(gem.depth - 1);
        
        // Base glow
        const baseGlow = this.scene.add.graphics();
        baseGlow.fillStyle(this.getSpecialColor(specialType), 0.3);
        baseGlow.fillCircle(0, 0, 50);
        baseGlow.setBlendMode(Phaser.BlendModes.ADD);
        glowContainer.add(baseGlow);
        
        // Pulsing animation
        this.scene.tweens.add({
            targets: baseGlow,
            scaleX: 1.2,
            scaleY: 1.2,
            alpha: 0.1,
            duration: 1000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        this.activeEffects.set(glowKey, glowContainer);
    }
    
    /**
     * Play activation effect khi special gem được activate
     */
    private playActivationEffect(gem: any, specialType: SpecialGemType): void {
        switch (specialType) {
            case SpecialGemType.STRIPED_HORIZONTAL:
            case SpecialGemType.STRIPED_VERTICAL:
                this.playStripedActivation(gem, specialType);
                break;
            case SpecialGemType.WRAPPED:
                this.playWrappedActivation(gem);
                break;
            case SpecialGemType.COLOR_BOMB:
                this.playColorBombActivation(gem);
                break;
        }
    }
    
    /**
     * Striped gem activation effect
     */
    private playStripedActivation(gem: any, specialType: SpecialGemType): void {
        const isHorizontal = specialType === SpecialGemType.STRIPED_HORIZONTAL;
        
        // Lightning effect
        const lightning = this.scene.add.graphics();
        lightning.lineStyle(8, 0xffffff, 1);
        
        if (isHorizontal) {
            lightning.lineBetween(0, gem.y, 1024, gem.y);
        } else {
            lightning.lineBetween(gem.x, 0, gem.x, 768);
        }
        
        lightning.setBlendMode(Phaser.BlendModes.ADD);
        
        // Flash and fade
        this.scene.tweens.add({
            targets: lightning,
            alpha: 0,
            duration: 300,
            ease: 'Power2.easeOut',
            onComplete: () => lightning.destroy()
        });
        
        // Screen shake
        this.scene.cameras.main.shake(200, 0.02);
    }
    
    /**
     * Wrapped gem activation effect
     */
    private playWrappedActivation(gem: any): void {
        // Explosion rings
        for (let i = 0; i < 3; i++) {
            this.scene.time.delayedCall(i * 100, () => {
                const ring = this.scene.add.graphics();
                ring.lineStyle(6, 0xffffff, 0.8);
                ring.strokeCircle(gem.x, gem.y, 20);
                ring.setBlendMode(Phaser.BlendModes.ADD);
                
                this.scene.tweens.add({
                    targets: ring,
                    scaleX: 4,
                    scaleY: 4,
                    alpha: 0,
                    duration: 400,
                    ease: 'Power2.easeOut',
                    onComplete: () => ring.destroy()
                });
            });
        }
        
        // Screen shake
        this.scene.cameras.main.shake(300, 0.03);
    }
    
    /**
     * Color bomb activation effect
     */
    private playColorBombActivation(gem: any): void {
        // Rainbow explosion
        const colors = [0xff0000, 0xff8800, 0xffff00, 0x00ff00, 0x0088ff, 0x8800ff];
        
        colors.forEach((color, index) => {
            this.scene.time.delayedCall(index * 50, () => {
                const burst = this.scene.add.particles(gem.x, gem.y, 'particle', {
                    speed: { min: 200, max: 400 },
                    scale: { start: 0.8, end: 0 },
                    alpha: { start: 1, end: 0 },
                    lifespan: 1000,
                    quantity: 20,
                    angle: { min: 0, max: 360 },
                    tint: color,
                    blendMode: 'ADD'
                });
                
                this.scene.time.delayedCall(1200, () => {
                    burst.destroy();
                });
            });
        });
        
        // Screen flash
        this.scene.cameras.main.flash(500, 255, 255, 255, false, 0.5);
        
        // Strong screen shake
        this.scene.cameras.main.shake(500, 0.05);
    }
    
    /**
     * Get color cho special gem type
     */
    private getSpecialColor(specialType: SpecialGemType): number {
        switch (specialType) {
            case SpecialGemType.STRIPED_HORIZONTAL:
            case SpecialGemType.STRIPED_VERTICAL:
                return 0xffffff;
            case SpecialGemType.WRAPPED:
                return 0x00ffff;
            case SpecialGemType.COLOR_BOMB:
                return 0xff00ff;
            default:
                return 0xffffff;
        }
    }
    
    /**
     * Update effect positions khi gems di chuyển
     */
    public updateEffectPosition(gem: any): void {
        const glowKey = `glow_${gem.gridX}_${gem.gridY}`;
        const effect = this.activeEffects.get(glowKey);
        if (effect) {
            effect.setPosition(gem.x, gem.y);
        }
    }
    
    /**
     * Remove effect cho gem
     */
    public removeEffect(gem: any): void {
        const glowKey = `glow_${gem.gridX}_${gem.gridY}`;
        const effect = this.activeEffects.get(glowKey);
        if (effect) {
            effect.destroy();
            this.activeEffects.delete(glowKey);
        }
    }
    
    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('special-gem-created', this.onSpecialGemCreated, this);
        this.scene.events.off('special-gem-activated', this.onSpecialGemActivated, this);
        
        this.activeEffects.forEach(effect => effect.destroy());
        this.activeEffects.clear();
    }
}
